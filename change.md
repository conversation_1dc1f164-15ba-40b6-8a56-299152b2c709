# 🚀 Kế hoạch Modernization - Qu<PERSON>n lý Thiết bị

## 📋 Tổng quan
Kế hoạch nâng cấp toàn diện dự án từ stack hiệ<PERSON> t<PERSON><PERSON> sang modern stack với TanStack ecosystem, Cloudflare D1, và Bun.

### 🎯 Mục tiêu ch<PERSON>h
- ✅ Giữ nguyên giao diện và UX hiện tại
- 🔄 Modernize tech stack
- 🚀 Cải thiện performance và DX
- 🛡️ Tăng cường type safety

---

## 📦 Phase 1: Package Manager Migration (Bun)

### 🎯 Mục tiêu
Chuyển từ npm/pnpm sang Bun để cải thiện tốc độ build và install.

### ✅ Tasks
- [x] **1.1** Cài đặt Bun globally ✅ **HOÀN THÀNH**
  ```bash
  npm install -g bun  # Đã cài đặt Bun v1.2.15 thành công
  ```

- [x] **1.2** Backup current lock files ✅ **HOÀN THÀNH**
  ```bash
  copy package-lock.json package-lock.json.backup  # Đã backup thành công
  copy bun.lockb bun.lockb.backup  # Đã backup thành công
  ```

- [x] **1.3** Remove old lock files và node_modules ✅ **HOÀN THÀNH**
  ```bash
  Remove-Item package-lock.json -Force  # Đã xóa thành công
  Remove-Item node_modules -Recurse -Force  # Đã xóa thành công
  ```

- [x] **1.4** Install dependencies với Bun ✅ **HOÀN THÀNH**
  ```bash
  bun install  # Đã cài 399 packages trong 26.23s - RẤT NHANH!
  bun pm trust @swc/core  # Đã trust postinstall script
  ```

- [x] **1.5** Update package.json scripts ✅ **HOÀN THÀNH**
  ```json
  {
    "scripts": {
      "dev": "bun run vite",
      "build": "bun run vite build",
      "build:dev": "bun run vite build --mode development",
      "lint": "bun run eslint .",
      "preview": "bun run vite preview"
    }
  }
  ```

- [x] **1.6** Test build và dev server ✅ **HOÀN THÀNH**
  ```bash
  bun run dev    # ✅ Dev server chạy thành công trên port 8080
  bun run build  # ✅ Build thành công trong 2.94s
  ```

### 🧪 Testing
- [ ] Verify app starts correctly
- [ ] Check all features work as before
- [ ] Verify build output

---

## 📚 Phase 2: Dependencies Update

### 🎯 Mục tiêu
Update tất cả dependencies lên phiên bản mới nhất.

### ✅ Tasks
- [ ] **2.1** Update React ecosystem
  ```bash
  bun add react@latest react-dom@latest
  bun add -d @types/react@latest @types/react-dom@latest
  ```

- [ ] **2.2** Update Vite và build tools
  ```bash
  bun add -d vite@latest @vitejs/plugin-react-swc@latest
  bun add -d typescript@latest
  ```

- [ ] **2.3** Update TanStack Query
  ```bash
  bun add @tanstack/react-query@latest
  ```

- [ ] **2.4** Update UI libraries
  ```bash
  bun add lucide-react@latest
  bun add class-variance-authority@latest clsx@latest
  bun add tailwind-merge@latest
  ```

- [ ] **2.5** Update Radix UI components
  ```bash
  bun add @radix-ui/react-accordion@latest @radix-ui/react-alert-dialog@latest
  # ... (update all @radix-ui packages)
  ```

### 🧪 Testing
- [ ] Run `bun run dev` và check console errors
- [ ] Test all UI components
- [ ] Verify TypeScript compilation

---

## 🔄 Phase 3: TanStack Migration

### 🎯 Mục tiêu
Migrate sang TanStack ecosystem (Router, Form, Table).

### ✅ Tasks

#### 3.1 TanStack Router Migration
- [ ] **3.1.1** Install TanStack Router
  ```bash
  bun add @tanstack/react-router
  bun add -d @tanstack/router-vite-plugin
  ```

- [ ] **3.1.2** Update vite.config.ts
  ```typescript
  import { TanStackRouterVite } from '@tanstack/router-vite-plugin'
  
  export default defineConfig({
    plugins: [react(), TanStackRouterVite()],
  })
  ```

- [ ] **3.1.3** Create route tree structure
  ```
  src/routes/
  ├── __root.tsx
  ├── index.tsx
  └── _404.tsx
  ```

- [ ] **3.1.4** Migrate App.tsx to use TanStack Router
- [ ] **3.1.5** Update routing logic in components

#### 3.2 TanStack Form Migration
- [ ] **3.2.1** Install TanStack Form
  ```bash
  bun add @tanstack/react-form
  ```

- [ ] **3.2.2** Migrate React Hook Form usage
- [ ] **3.2.3** Update form validation với Zod integration

#### 3.3 TanStack Table (if needed)
- [ ] **3.3.1** Install TanStack Table
  ```bash
  bun add @tanstack/react-table
  ```

- [ ] **3.3.2** Create table components for equipment lists
- [ ] **3.3.3** Add sorting, filtering, pagination

### 🧪 Testing
- [ ] Test navigation between pages
- [ ] Test form submissions
- [ ] Test table interactions

---

## 🎨 Phase 4: Zod v4 & Tailwind v4 Migration

### 🎯 Mục tiêu
Upgrade Zod và Tailwind lên phiên bản mới nhất.

### ✅ Tasks

#### 4.1 Zod v4 Migration
- [ ] **4.1.1** Update Zod
  ```bash
  bun add zod@latest
  ```

- [ ] **4.1.2** Review breaking changes
- [ ] **4.1.3** Update schema definitions
- [ ] **4.1.4** Test form validations

#### 4.2 Tailwind v4 Migration
- [ ] **4.2.1** Update Tailwind CSS
  ```bash
  bun add -d tailwindcss@latest autoprefixer@latest postcss@latest
  ```

- [ ] **4.2.2** Update tailwind.config.ts for v4 syntax
- [ ] **4.2.3** Review CSS classes for breaking changes
- [ ] **4.2.4** Update @tailwindcss/typography if needed

### 🧪 Testing
- [ ] Verify all styling remains intact
- [ ] Test responsive design
- [ ] Check form validations

---

## 🗄️ Phase 5: Backend Setup (D1 + Hono + Drizzle)

### 🎯 Mục tiêu
Setup backend với Cloudflare D1, Hono, và Drizzle ORM.

### ✅ Tasks

#### 5.1 Cloudflare Setup
- [ ] **5.1.1** Install Wrangler CLI
  ```bash
  bun add -d wrangler
  ```

- [ ] **5.1.2** Create wrangler.toml
- [ ] **5.1.3** Setup D1 database
  ```bash
  bunx wrangler d1 create quan-ly-ttb-db
  ```

#### 5.2 Hono API Setup
- [ ] **5.2.1** Install Hono
  ```bash
  bun add hono
  ```

- [ ] **5.2.2** Create API structure
  ```
  src/api/
  ├── index.ts
  ├── routes/
  │   ├── walkie-talkies.ts
  │   ├── access-cards.ts
  │   └── history.ts
  └── middleware/
  ```

- [ ] **5.2.3** Setup CORS và middleware

#### 5.3 Drizzle ORM Setup
- [ ] **5.3.1** Install Drizzle
  ```bash
  bun add drizzle-orm
  bun add -d drizzle-kit
  ```

- [ ] **5.3.2** Create database schema
  ```
  src/db/
  ├── schema.ts
  ├── migrations/
  └── seed.ts
  ```

- [ ] **5.3.3** Setup migrations
- [ ] **5.3.4** Create seed data từ mock data hiện tại

#### 5.4 Integration
- [ ] **5.4.1** Update TanStack Query để call APIs
- [ ] **5.4.2** Replace mock data với real API calls
- [ ] **5.4.3** Add error handling và loading states

### 🧪 Testing
- [ ] Test API endpoints
- [ ] Test database operations
- [ ] Test frontend integration

---

## 🚀 Phase 6: Cloudflare Workers Deployment

### 🎯 Mục tiêu
Deploy ứng dụng full-stack lên Cloudflare Workers với static assets và API.

### ✅ Tasks

#### 6.1 Wrangler Configuration
- [ ] **6.1.1** Create wrangler.jsonc configuration
  ```jsonc
  {
    "name": "quan-ly-ttb",
    "main": "src/api/index.ts",
    "compatibility_date": "2024-01-01",
    "assets": {
      "directory": "./dist/client",
      "not_found_handling": "single-page-application"
    },
    "d1_databases": [
      {
        "binding": "DB",
        "database_name": "quan-ly-ttb-db",
        "database_id": "<DATABASE_ID>"
      }
    ],
    "vars": {
      "NODE_ENV": "production"
    }
  }
  ```

- [ ] **6.1.2** Update package.json scripts
  ```json
  {
    "scripts": {
      "dev": "bun run vite",
      "build": "bun run vite build",
      "deploy": "bunx wrangler deploy",
      "preview": "bunx wrangler dev",
      "db:migrate": "bunx wrangler d1 migrations apply quan-ly-ttb-db",
      "db:seed": "bunx wrangler d1 execute quan-ly-ttb-db --file=./src/db/seed.sql"
    }
  }
  ```

#### 6.2 Vite Configuration for Workers
- [ ] **6.2.1** Install Cloudflare Vite plugin
  ```bash
  bun add -d @cloudflare/vite-plugin
  ```

- [ ] **6.2.2** Update vite.config.ts
  ```typescript
  import { defineConfig } from 'vite'
  import react from '@vitejs/plugin-react-swc'
  import { cloudflare } from '@cloudflare/vite-plugin'

  export default defineConfig({
    plugins: [
      react(),
      cloudflare({
        main: 'src/api/index.ts',
        assets: {
          directory: './dist/client'
        }
      })
    ],
    build: {
      outDir: 'dist/client',
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            ui: ['@radix-ui/react-accordion', '@radix-ui/react-dialog']
          }
        }
      }
    }
  })
  ```

#### 6.3 API Structure for Workers
- [ ] **6.3.1** Create Workers entry point
  ```typescript
  // src/api/index.ts
  import { Hono } from 'hono'
  import { cors } from 'hono/cors'
  import { serveStatic } from 'hono/cloudflare-workers'
  import walkieTalkiesRoutes from './routes/walkie-talkies'
  import accessCardsRoutes from './routes/access-cards'
  import historyRoutes from './routes/history'

  type Bindings = {
    DB: D1Database
    ASSETS: Fetcher
  }

  const app = new Hono<{ Bindings: Bindings }>()

  app.use('*', cors())
  app.route('/api/walkie-talkies', walkieTalkiesRoutes)
  app.route('/api/access-cards', accessCardsRoutes)
  app.route('/api/history', historyRoutes)

  // Serve static assets
  app.get('*', serveStatic({ root: './' }))
  app.get('*', serveStatic({ path: './index.html' }))

  export default app
  ```

- [ ] **6.3.2** Update API routes for D1 integration
- [ ] **6.3.3** Add proper error handling và logging

#### 6.4 Database Migration for Production
- [ ] **6.4.1** Create production D1 database
  ```bash
  bunx wrangler d1 create quan-ly-ttb-db
  ```

- [ ] **6.4.2** Run migrations
  ```bash
  bunx wrangler d1 migrations apply quan-ly-ttb-db
  ```

- [ ] **6.4.3** Seed production data
  ```bash
  bunx wrangler d1 execute quan-ly-ttb-db --file=./src/db/seed.sql
  ```

#### 6.5 Environment Configuration
- [ ] **6.5.1** Setup environment variables
  ```bash
  bunx wrangler secret put API_SECRET
  bunx wrangler secret put JWT_SECRET
  ```

- [ ] **6.5.2** Configure different environments
  ```jsonc
  {
    "env": {
      "staging": {
        "name": "quan-ly-ttb-staging",
        "d1_databases": [
          {
            "binding": "DB",
            "database_name": "quan-ly-ttb-staging-db",
            "database_id": "<STAGING_DATABASE_ID>"
          }
        ]
      },
      "production": {
        "name": "quan-ly-ttb-prod",
        "d1_databases": [
          {
            "binding": "DB",
            "database_name": "quan-ly-ttb-db",
            "database_id": "<PROD_DATABASE_ID>"
          }
        ]
      }
    }
  }
  ```

#### 6.6 Custom Domain Setup
- [ ] **6.6.1** Add custom domain in Cloudflare dashboard
- [ ] **6.6.2** Configure DNS records
- [ ] **6.6.3** Setup SSL/TLS encryption

### 🧪 Testing
- [ ] Test local development với `bunx wrangler dev`
- [ ] Test staging deployment
- [ ] Test production deployment
- [ ] Verify API endpoints work
- [ ] Test static asset serving
- [ ] Check SPA routing works correctly

---

## 🧪 Phase 7: Final Testing & Optimization

### ✅ Tasks
- [ ] **7.1** End-to-end testing
- [ ] **7.2** Performance optimization
- [ ] **7.3** Bundle size analysis
- [ ] **7.4** Accessibility testing
- [ ] **7.5** Mobile responsiveness
- [ ] **7.6** Error boundary setup
- [ ] **7.7** Production build testing
- [ ] **7.8** Workers analytics setup
- [ ] **7.9** Monitoring và alerting

### 📊 Success Metrics
- [ ] App loads < 2s
- [ ] All features work as before
- [ ] No console errors
- [ ] TypeScript compilation success
- [ ] Bundle size không tăng đáng kể
- [ ] Workers deployment successful
- [ ] API response time < 500ms
- [ ] Static assets cached properly

---

## 🔄 Rollback Plan

### Nếu có issues:
1. **Phase 1**: Restore package-lock.json, remove bun.lockb
2. **Phase 2**: Downgrade specific packages
3. **Phase 3**: Keep React Router DOM as fallback
4. **Phase 4**: Revert to previous versions
5. **Phase 5**: Use mock data temporarily
6. **Phase 6**: Rollback to local development
7. **Phase 7**: Revert to previous working deployment

### Emergency Commands:
```bash
# Restore npm
rm bun.lockb && npm install

# Revert git changes
git checkout -- package.json
git clean -fd

# Rollback Workers deployment
bunx wrangler rollback

# Delete Workers deployment
bunx wrangler delete quan-ly-ttb
```

---

## 🌐 Deployment Commands Summary

### Development:
```bash
# Local development với Workers runtime
bunx wrangler dev

# Local development với Vite only
bun run dev
```

### Staging:
```bash
# Deploy to staging
bunx wrangler deploy --env staging

# Check staging logs
bunx wrangler tail --env staging
```

### Production:
```bash
# Deploy to production
bunx wrangler deploy --env production

# Check production logs
bunx wrangler tail --env production

# Monitor performance
bunx wrangler analytics
```

---

## 📝 Notes
- Mỗi phase nên được test kỹ trước khi chuyển sang phase tiếp theo
- Commit code sau mỗi phase thành công
- Backup database trước khi migrate
- Document breaking changes và solutions
- **Cloudflare Workers** khác với **Cloudflare Pages** - Workers có nhiều tính năng hơn
- Static assets trên Workers vẫn miễn phí như Pages
- Workers có thể handle cả frontend và backend trong cùng 1 deployment
- Sử dụng `wrangler.jsonc` thay vì `wrangler.toml` cho modern config
- D1 database binding được configure trong wrangler config
- SPA routing cần `not_found_handling: "single-page-application"`
